
import socket
import subprocess
import time
import threading
from socketserver import Threading<PERSON>PServer, BaseRequestHandler
import logging

# Configuration
DISPLAY_IP = "*************"
DISPLAY_PORT = 7010
DISPLAY_MAC = "dc:62:94:3a:19:57"
PROXY_PORT = 7012
AZURE_IP = "************"
AZURE_PORT = 7010

# Setup logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(message)s")
logger = logging.getLogger(__name__)

class SmartProxyHandler(BaseRequestHandler):
    def handle(self):
        client_addr = self.client_address[0]
        logger.info(f"New connection from {client_addr}")
        
        try:
            # Connect to Azure backend
            azure_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            azure_socket.settimeout(10)
            
            # Connect to Azure
            azure_socket.connect((AZURE_IP, AZURE_PORT))
            logger.info(f"Connected to Azure backend at {AZURE_IP}:{AZURE_PORT}")
            
            # Proxy the connection
            self.proxy_data(self.request, azure_socket)
            
        except Exception as e:
            logger.error(f"Error handling connection: {e}")
        finally:
            try:
                azure_socket.close()
            except:
                pass
    
    def proxy_data(self, client_socket, server_socket):
        """Proxy data between client and server sockets"""
        def forward(source, destination, direction):
            try:
                while True:
                    data = source.recv(4096)
                    if not data:
                        break
                    destination.send(data)
            except Exception as e:
                logger.debug(f"Forward {direction} ended: {e}")
            finally:
                try:
                    source.close()
                except:
                    pass
                try:
                    destination.close()
                except:
                    pass
        
        # Start forwarding in both directions
        client_to_server = threading.Thread(
            target=forward, 
            args=(client_socket, server_socket, "client->server")
        )
        server_to_client = threading.Thread(
            target=forward, 
            args=(server_socket, client_socket, "server->client")
        )
        
        client_to_server.daemon = True
        server_to_client.daemon = True
        
        client_to_server.start()
        server_to_client.start()
        
        client_to_server.join()
        server_to_client.join()

if __name__ == "__main__":
    try:
        server = ThreadingTCPServer(("*************", PROXY_PORT), SmartProxyHandler)
        logger.info(f"Smart display proxy starting on *************:{PROXY_PORT}")
        logger.info(f"Proxying to Azure: {AZURE_IP}:{AZURE_PORT}")
        logger.info(f"Display: {DISPLAY_IP} (MAC: {DISPLAY_MAC})")
        server.serve_forever()
    except KeyboardInterrupt:
        logger.info("Shutting down proxy server...")
    except Exception as e:
        logger.error(f"Failed to start server: {e}")
