# nginx.conf

include /etc/nginx/modules-enabled/*.conf;

worker_processes  1;

events {
    worker_connections 1024;
}

stream {
    # Enable detailed logging for debugging
    error_log /var/log/nginx/stream.log info;
   
    # AZURE PROXY: Display connects to local IP, traffic goes to Azure
    server {
        listen *************:7010;
        proxy_pass ************:7010;
        proxy_timeout 300s;
        proxy_connect_timeout 30s;
        proxy_responses 1;
        proxy_bind *************;
        proxy_socket_keepalive on;
    }
   
    # Alternative Azure proxy port for testing
    server {
        listen *************:7011;
        proxy_pass ************:7010;
        proxy_timeout 300s;
        proxy_connect_timeout 30s;
        proxy_responses 1;
        proxy_bind *************;
        proxy_socket_keepalive on;
    }
}
